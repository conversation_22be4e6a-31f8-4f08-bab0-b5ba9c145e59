#!/usr/bin/env python3
"""
Offline AI Service for KCC Query Assistant
Provides local LLM capabilities using Ollama API
"""

import json
import time
import logging
from typing import Dict, List, Optional, Generator, Any
from dataclasses import dataclass
from pathlib import Path

import requests
from pydantic import BaseModel, Field

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class GenerationRequest:
    """Request configuration for text generation"""
    prompt: str
    system_prompt: Optional[str] = None
    temperature: float = 0.7
    max_tokens: int = 1000
    top_p: float = 0.9
    top_k: int = 40
    stream: bool = False


@dataclass
class GenerationResponse:
    """Response from text generation"""
    response: str
    model: str
    total_duration: Optional[int] = None
    load_duration: Optional[int] = None
    prompt_eval_count: Optional[int] = None
    eval_count: Optional[int] = None
    eval_duration: Optional[int] = None


class ModelManager:
    """Manages Ollama models and operations"""

    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.api_url = f"{base_url}/api"

    def is_ollama_running(self) -> bool:
        """Check if Ollama service is running"""
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            return response.status_code == 200
        except requests.exceptions.RequestException:
            return False

    def list_models(self) -> List[str]:
        """List available models"""
        try:
            response = requests.get(f"{self.api_url}/tags", timeout=10)
            if response.status_code == 200:
                data = response.json()
                return [model["name"] for model in data.get("models", [])]
            return []
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to list models: {e}")
            return []

    def pull_model(self, model_name: str) -> bool:
        """Pull/download a model"""
        try:
            logger.info(f"Pulling model: {model_name}")
            response = requests.post(
                f"{self.api_url}/pull",
                json={"name": model_name},
                stream=True,
                timeout=300
            )

            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                        data = json.loads(line)
                        if "status" in data:
                            logger.info(f"Pull status: {data['status']}")
                        if data.get("status") == "success":
                            return True
            return False
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to pull model {model_name}: {e}")
            return False

    def delete_model(self, model_name: str) -> bool:
        """Delete a model"""
        try:
            response = requests.delete(
                f"{self.api_url}/delete",
                json={"name": model_name},
                timeout=30
            )
            return response.status_code == 200
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to delete model {model_name}: {e}")
            return False

    def benchmark_model(self, model_name: str, prompt: str) -> Optional[Dict[str, Any]]:
        """Benchmark model performance"""
        try:
            start_time = time.time()

            response = requests.post(
                f"{self.api_url}/generate",
                json={
                    "model": model_name,
                    "prompt": prompt,
                    "stream": False
                },
                timeout=60
            )

            end_time = time.time()

            if response.status_code == 200:
                data = response.json()
                return {
                    "model": model_name,
                    "total_time": end_time - start_time,
                    "total_duration": data.get("total_duration", 0) / 1e9,  # Convert to seconds
                    "load_duration": data.get("load_duration", 0) / 1e9,
                    "prompt_eval_count": data.get("prompt_eval_count", 0),
                    "eval_count": data.get("eval_count", 0),
                    "eval_duration": data.get("eval_duration", 0) / 1e9,
                    "tokens_per_second": data.get("eval_count", 0) / (data.get("eval_duration", 1) / 1e9),
                    "response_length": len(data.get("response", ""))
                }
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"Benchmark failed for {model_name}: {e}")
            return None


class OfflineAIService:
    """Main offline AI service using Ollama"""

    def __init__(self, base_url: str = "http://localhost:11434", default_model: str = None):
        self.base_url = base_url
        self.api_url = f"{base_url}/api"
        self.model_manager = ModelManager(base_url)

        # Auto-detect available model if none specified
        if default_model is None:
            available_models = self.model_manager.list_models()
            if available_models:
                self.current_model = available_models[0]
                self.default_model = available_models[0]
                logger.info(f"Auto-selected model: {self.current_model}")
            else:
                self.current_model = "gemma2:2b"  # Fallback
                self.default_model = "gemma2:2b"
        else:
            self.default_model = default_model
            self.current_model = default_model

    def is_service_ready(self) -> bool:
        """Check if the AI service is ready"""
        if not self.model_manager.is_ollama_running():
            return False

        available_models = self.model_manager.list_models()
        return len(available_models) > 0

    def get_service_status(self) -> Dict[str, Any]:
        """Get comprehensive service status"""
        is_running = self.model_manager.is_ollama_running()
        available_models = self.model_manager.list_models() if is_running else []

        return {
            "service_ready": is_running and len(available_models) > 0,
            "ollama_running": is_running,
            "current_model": self.current_model,
            "available_models": available_models,
            "base_url": self.base_url
        }

    def set_model(self, model_name: str) -> bool:
        """Set the current model"""
        available_models = self.model_manager.list_models()
        if model_name in available_models:
            self.current_model = model_name
            return True
        return False

    def generate_text(self, request: GenerationRequest) -> GenerationResponse:
        """Generate text using the current model"""
        try:
            payload = {
                "model": self.current_model,
                "prompt": request.prompt,
                "stream": False,
                "options": {
                    "temperature": request.temperature,
                    "num_predict": request.max_tokens,
                    "top_p": request.top_p,
                    "top_k": request.top_k
                }
            }

            if request.system_prompt:
                payload["system"] = request.system_prompt

            response = requests.post(
                f"{self.api_url}/generate",
                json=payload,
                timeout=120
            )

            if response.status_code == 200:
                data = response.json()
                return GenerationResponse(
                    response=data.get("response", ""),
                    model=self.current_model,
                    total_duration=data.get("total_duration"),
                    load_duration=data.get("load_duration"),
                    prompt_eval_count=data.get("prompt_eval_count"),
                    eval_count=data.get("eval_count"),
                    eval_duration=data.get("eval_duration")
                )
            else:
                raise Exception(f"API request failed with status {response.status_code}: {response.text}")

        except requests.exceptions.RequestException as e:
            logger.error(f"Text generation failed: {e}")
            raise Exception(f"Failed to generate text: {e}")

    def generate_text_stream(self, request: GenerationRequest) -> Generator[str, None, None]:
        """Generate text with streaming response"""
        try:
            payload = {
                "model": self.current_model,
                "prompt": request.prompt,
                "stream": True,
                "options": {
                    "temperature": request.temperature,
                    "num_predict": request.max_tokens,
                    "top_p": request.top_p,
                    "top_k": request.top_k
                }
            }

            if request.system_prompt:
                payload["system"] = request.system_prompt

            response = requests.post(
                f"{self.api_url}/generate",
                json=payload,
                stream=True,
                timeout=120
            )

            if response.status_code == 200:
                for line in response.iter_lines():
                    if line:
                        data = json.loads(line)
                        if "response" in data:
                            yield data["response"]
                        if data.get("done", False):
                            break
            else:
                raise Exception(f"Streaming request failed with status {response.status_code}")

        except requests.exceptions.RequestException as e:
            logger.error(f"Streaming generation failed: {e}")
            raise Exception(f"Failed to stream text: {e}")

    def chat_completion(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """Chat completion with message history"""
        try:
            payload = {
                "model": self.current_model,
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": temperature
                }
            }

            response = requests.post(
                f"{self.api_url}/chat",
                json=payload,
                timeout=120
            )

            if response.status_code == 200:
                data = response.json()
                return data.get("message", {}).get("content", "")
            else:
                raise Exception(f"Chat completion failed with status {response.status_code}")

        except requests.exceptions.RequestException as e:
            logger.error(f"Chat completion failed: {e}")
            raise Exception(f"Failed to complete chat: {e}")

    def analyze_data(self, data_summary: str, analysis_type: str = "summary") -> str:
        """Analyze data using AI with specific analysis type"""
        analysis_prompts = {
            "summary": "Provide a comprehensive summary of this dataset, highlighting key characteristics and patterns.",
            "insights": "Generate actionable insights and interesting findings from this dataset.",
            "recommendations": "Provide recommendations for data analysis, cleaning, or further investigation.",
            "trends": "Identify trends, patterns, and anomalies in this dataset."
        }

        system_prompt = f"""You are an expert data analyst. {analysis_prompts.get(analysis_type, analysis_prompts['summary'])}
        Focus on agricultural data and provide practical, actionable advice for farmers and agricultural experts."""

        request = GenerationRequest(
            prompt=f"Analyze this dataset:\n\n{data_summary}",
            system_prompt=system_prompt,
            temperature=0.3,
            max_tokens=1500
        )

        response = self.generate_text(request)
        return response.response
