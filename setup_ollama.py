#!/usr/bin/env python3
"""
Ollama Setup Script for KCC Query Assistant
Handles Ollama installation, model downloading, and configuration
"""

import os
import sys
import time
import platform
import subprocess
import requests
from pathlib import Path
from typing import List, Optional

from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Confirm, Prompt

from offline_ai_service import ModelManager, OfflineAIService, GenerationRequest

console = Console()


class OllamaSetup:
    """Handles Ollama installation and setup"""

    def __init__(self):
        self.system = platform.system().lower()
        self.model_manager = ModelManager()
        self.recommended_models = [
            "gemma2:2b",      # Fast, lightweight
            "gemma2:9b",      # Better quality
            "llama3.2:3b",    # Alternative option
            "qwen2.5:3b",     # Good for multilingual
        ]

    def check_ollama_installed(self) -> bool:
        """Check if Ollama is installed"""
        try:
            result = subprocess.run(
                ["ollama", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    def install_ollama(self) -> bool:
        """Install Ollama based on the operating system"""
        console.print(Panel.fit(
            "[bold blue]Installing Ollama...[/bold blue]",
            title="🚀 Installation"
        ))

        try:
            if self.system == "windows":
                return self._install_ollama_windows()
            elif self.system == "darwin":  # macOS
                return self._install_ollama_macos()
            elif self.system == "linux":
                return self._install_ollama_linux()
            else:
                console.print(f"[red]❌ Unsupported operating system: {self.system}[/red]")
                return False
        except Exception as e:
            console.print(f"[red]❌ Installation failed: {e}[/red]")
            return False

    def _install_ollama_windows(self) -> bool:
        """Install Ollama on Windows"""
        console.print("[yellow]📥 Downloading Ollama for Windows...[/yellow]")

        # Download URL for Windows
        download_url = "https://ollama.com/download/windows"

        console.print(f"[blue]Please download and install Ollama from: {download_url}[/blue]")
        console.print("[yellow]After installation, restart this script.[/yellow]")

        # Open the download page
        try:
            import webbrowser
            webbrowser.open(download_url)
        except:
            pass

        return False  # Manual installation required

    def _install_ollama_macos(self) -> bool:
        """Install Ollama on macOS"""
        console.print("[yellow]📥 Installing Ollama on macOS...[/yellow]")

        try:
            # Try using Homebrew first
            result = subprocess.run(
                ["brew", "install", "ollama"],
                capture_output=True,
                text=True,
                timeout=300
            )

            if result.returncode == 0:
                console.print("[green]✅ Ollama installed via Homebrew[/green]")
                return True
            else:
                # Fallback to curl installation
                return self._install_ollama_curl()

        except FileNotFoundError:
            # Homebrew not found, use curl
            return self._install_ollama_curl()

    def _install_ollama_linux(self) -> bool:
        """Install Ollama on Linux"""
        console.print("[yellow]📥 Installing Ollama on Linux...[/yellow]")
        return self._install_ollama_curl()

    def _install_ollama_curl(self) -> bool:
        """Install Ollama using curl script"""
        try:
            console.print("[yellow]📥 Downloading Ollama installation script...[/yellow]")

            result = subprocess.run([
                "curl", "-fsSL", "https://ollama.com/install.sh"
            ], capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                # Run the installation script
                console.print("[yellow]🔧 Running installation script...[/yellow]")

                process = subprocess.run([
                    "sh", "-c", result.stdout
                ], capture_output=True, text=True, timeout=300)

                if process.returncode == 0:
                    console.print("[green]✅ Ollama installed successfully[/green]")
                    return True
                else:
                    console.print(f"[red]❌ Installation failed: {process.stderr}[/red]")
                    return False
            else:
                console.print(f"[red]❌ Failed to download installation script: {result.stderr}[/red]")
                return False

        except subprocess.TimeoutExpired:
            console.print("[red]❌ Installation timed out[/red]")
            return False
        except Exception as e:
            console.print(f"[red]❌ Installation error: {e}[/red]")
            return False

    def start_ollama_service(self) -> bool:
        """Start the Ollama service"""
        console.print("[yellow]🚀 Starting Ollama service...[/yellow]")

        try:
            if self.system == "windows":
                # On Windows, Ollama should start automatically after installation
                # Check if it's running
                time.sleep(5)  # Give it time to start
                return self.model_manager.is_ollama_running()
            else:
                # On Unix systems, start the service
                subprocess.Popen(
                    ["ollama", "serve"],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL
                )

                # Wait for service to start
                for i in range(30):  # Wait up to 30 seconds
                    if self.model_manager.is_ollama_running():
                        console.print("[green]✅ Ollama service started[/green]")
                        return True
                    time.sleep(1)

                console.print("[red]❌ Ollama service failed to start[/red]")
                return False

        except Exception as e:
            console.print(f"[red]❌ Failed to start Ollama service: {e}[/red]")
            return False

    def download_model(self, model_name: str) -> bool:
        """Download a specific model"""
        console.print(f"[yellow]📥 Downloading model: {model_name}[/yellow]")
        console.print("[blue]This may take several minutes depending on model size...[/blue]")

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task(f"Downloading {model_name}...", total=None)

            success = self.model_manager.pull_model(model_name)

            if success:
                progress.update(task, description=f"✅ {model_name} downloaded successfully")
                console.print(f"[green]✅ Model {model_name} is ready![/green]")
                return True
            else:
                progress.update(task, description=f"❌ Failed to download {model_name}")
                console.print(f"[red]❌ Failed to download {model_name}[/red]")
                return False

    def setup_recommended_model(self) -> Optional[str]:
        """Setup a recommended model for the KCC assistant"""
        console.print(Panel.fit(
            "[bold blue]Model Selection[/bold blue]\n"
            "Choose a model for your KCC Query Assistant",
            title="🤖 AI Model Setup"
        ))

        # Check available models
        available_models = self.model_manager.list_models()

        if available_models:
            console.print("[green]✅ Found existing models:[/green]")
            for model in available_models:
                console.print(f"  • {model}")

            use_existing = Confirm.ask("Use an existing model?")
            if use_existing:
                model_choice = Prompt.ask(
                    "Enter model name",
                    choices=available_models,
                    default=available_models[0]
                )
                return model_choice

        # Download a new model
        console.print("\n[blue]📋 Recommended models for KCC Assistant:[/blue]")
        for i, model in enumerate(self.recommended_models, 1):
            size_info = {
                "gemma2:2b": "~1.6GB - Fast, good for basic queries",
                "gemma2:9b": "~5.4GB - Better quality responses",
                "llama3.2:3b": "~2.0GB - Good balance of speed and quality",
                "qwen2.5:3b": "~1.9GB - Excellent for multilingual support"
            }
            console.print(f"  {i}. {model} - {size_info.get(model, 'Good general purpose model')}")

        choice = Prompt.ask(
            "Select a model to download",
            choices=[str(i) for i in range(1, len(self.recommended_models) + 1)],
            default="1"
        )

        selected_model = self.recommended_models[int(choice) - 1]

        if self.download_model(selected_model):
            return selected_model

        return None

    def verify_setup(self) -> bool:
        """Verify that everything is set up correctly"""
        console.print(Panel.fit(
            "[bold blue]Verifying Setup...[/bold blue]",
            title="🔍 Verification"
        ))

        # Check Ollama service
        if not self.model_manager.is_ollama_running():
            console.print("[red]❌ Ollama service is not running[/red]")
            return False

        console.print("[green]✅ Ollama service is running[/green]")

        # Check available models
        models = self.model_manager.list_models()
        if not models:
            console.print("[red]❌ No models available[/red]")
            return False

        console.print(f"[green]✅ Found {len(models)} model(s)[/green]")

        # Test AI service
        try:
            ai_service = OfflineAIService()
            if ai_service.is_service_ready():
                console.print("[green]✅ AI service is ready[/green]")

                # Quick test
                test_response = ai_service.generate_text(
                    GenerationRequest(
                        prompt="Hello! Can you help with agricultural questions?",
                        max_tokens=50
                    )
                )

                if test_response.response:
                    console.print("[green]✅ Model test successful[/green]")
                    console.print(f"[blue]Test response: {test_response.response[:100]}...[/blue]")
                    return True
                else:
                    console.print("[red]❌ Model test failed - no response[/red]")
                    return False
            else:
                console.print("[red]❌ AI service is not ready[/red]")
                return False

        except Exception as e:
            console.print(f"[red]❌ AI service test failed: {e}[/red]")
            return False


def main():
    """Main setup function"""
    console.print(Panel.fit(
        "[bold green]KCC Query Assistant - Ollama Setup[/bold green]\n"
        "This script will set up Ollama and download AI models for offline use",
        title="🌾 Agricultural AI Setup"
    ))

    setup = OllamaSetup()

    # Check if Ollama is already installed
    if setup.check_ollama_installed():
        console.print("[green]✅ Ollama is already installed[/green]")
    else:
        console.print("[yellow]⚠️  Ollama is not installed[/yellow]")

        install_confirm = Confirm.ask("Install Ollama now?")
        if not install_confirm:
            console.print("[yellow]👋 Setup cancelled[/yellow]")
            return

        if not setup.install_ollama():
            console.print("[red]❌ Ollama installation failed[/red]")
            return

    # Start Ollama service
    if not setup.model_manager.is_ollama_running():
        if not setup.start_ollama_service():
            console.print("[red]❌ Failed to start Ollama service[/red]")
            return

    # Setup model
    model_name = setup.setup_recommended_model()
    if not model_name:
        console.print("[red]❌ Model setup failed[/red]")
        return

    # Verify everything works
    if setup.verify_setup():
        console.print(Panel.fit(
            f"[bold green]🎉 Setup Complete![/bold green]\n\n"
            f"✅ Ollama service is running\n"
            f"✅ Model '{model_name}' is ready\n"
            f"✅ AI service is functional\n\n"
            f"You can now run the KCC Query Assistant!",
            title="🌾 Success"
        ))
    else:
        console.print(Panel.fit(
            "[bold red]❌ Setup verification failed[/bold red]\n"
            "Please check the error messages above and try again.",
            title="⚠️  Setup Issues"
        ))


if __name__ == "__main__":
    main()
