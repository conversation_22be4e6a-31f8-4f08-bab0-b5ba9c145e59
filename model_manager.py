#!/usr/bin/env python3
"""
Model Manager for KCC Query Assistant
Advanced model management, optimization, and monitoring utilities
"""

import json
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime

import requests
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

console = Console()
logger = logging.getLogger(__name__)


@dataclass
class ModelInfo:
    """Information about a model"""
    name: str
    size: int
    modified_at: str
    digest: str
    family: str = ""
    parameter_size: str = ""
    quantization_level: str = ""


@dataclass
class ModelPerformance:
    """Model performance metrics"""
    model_name: str
    avg_tokens_per_second: float
    avg_response_time: float
    memory_usage: int
    test_count: int
    last_tested: str
    quality_score: Optional[float] = None


class ModelOptimizer:
    """Optimizes model performance and settings"""
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.api_url = f"{base_url}/api"
    
    def get_optimal_settings(self, model_name: str, use_case: str = "general") -> Dict[str, Any]:
        """Get optimal settings for a model based on use case"""
        
        # Base settings for different use cases
        settings_profiles = {
            "general": {
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 40,
                "repeat_penalty": 1.1,
                "num_ctx": 2048
            },
            "factual": {  # For factual Q&A like KCC queries
                "temperature": 0.3,
                "top_p": 0.8,
                "top_k": 20,
                "repeat_penalty": 1.05,
                "num_ctx": 4096
            },
            "creative": {
                "temperature": 0.9,
                "top_p": 0.95,
                "top_k": 50,
                "repeat_penalty": 1.2,
                "num_ctx": 2048
            },
            "analytical": {  # For data analysis
                "temperature": 0.4,
                "top_p": 0.85,
                "top_k": 30,
                "repeat_penalty": 1.0,
                "num_ctx": 4096
            }
        }
        
        base_settings = settings_profiles.get(use_case, settings_profiles["general"])
        
        # Model-specific optimizations
        if "gemma" in model_name.lower():
            base_settings.update({
                "repeat_penalty": 1.05,
                "num_ctx": min(base_settings["num_ctx"], 8192)
            })
        elif "llama" in model_name.lower():
            base_settings.update({
                "repeat_penalty": 1.1,
                "num_ctx": min(base_settings["num_ctx"], 4096)
            })
        elif "qwen" in model_name.lower():
            base_settings.update({
                "temperature": base_settings["temperature"] * 0.9,  # Qwen tends to be more creative
                "num_ctx": min(base_settings["num_ctx"], 32768)
            })
        
        return base_settings
    
    def benchmark_model_comprehensive(self, model_name: str) -> ModelPerformance:
        """Run comprehensive benchmark on a model"""
        console.print(f"[yellow]🔄 Running comprehensive benchmark for {model_name}...[/yellow]")
        
        test_prompts = [
            "What are the best practices for wheat cultivation in India?",
            "How can farmers manage pest control in rice crops?",
            "Explain the benefits of organic farming methods.",
            "What are the symptoms of nitrogen deficiency in plants?",
            "How does crop rotation improve soil health?"
        ]
        
        total_tokens = 0
        total_time = 0
        test_count = len(test_prompts)
        
        for i, prompt in enumerate(test_prompts, 1):
            console.print(f"[blue]Running test {i}/{test_count}...[/blue]")
            
            try:
                start_time = time.time()
                
                response = requests.post(
                    f"{self.api_url}/generate",
                    json={
                        "model": model_name,
                        "prompt": prompt,
                        "stream": False,
                        "options": self.get_optimal_settings(model_name, "factual")
                    },
                    timeout=120
                )
                
                end_time = time.time()
                
                if response.status_code == 200:
                    data = response.json()
                    eval_count = data.get("eval_count", 0)
                    eval_duration = data.get("eval_duration", 0) / 1e9  # Convert to seconds
                    
                    total_tokens += eval_count
                    total_time += (end_time - start_time)
                else:
                    console.print(f"[red]❌ Test {i} failed[/red]")
                    test_count -= 1
                    
            except Exception as e:
                console.print(f"[red]❌ Test {i} error: {e}[/red]")
                test_count -= 1
        
        if test_count > 0:
            avg_tokens_per_second = total_tokens / total_time if total_time > 0 else 0
            avg_response_time = total_time / test_count
            
            performance = ModelPerformance(
                model_name=model_name,
                avg_tokens_per_second=avg_tokens_per_second,
                avg_response_time=avg_response_time,
                memory_usage=0,  # Would need system monitoring for this
                test_count=test_count,
                last_tested=datetime.now().isoformat(),
                quality_score=None  # Could implement quality scoring
            )
            
            console.print(f"[green]✅ Benchmark complete for {model_name}[/green]")
            return performance
        else:
            raise Exception("All benchmark tests failed")


class ModelRegistry:
    """Registry for managing model information and performance data"""
    
    def __init__(self, registry_file: str = "model_registry.json"):
        self.registry_file = Path(registry_file)
        self.models: Dict[str, Dict[str, Any]] = {}
        self.load_registry()
    
    def load_registry(self):
        """Load model registry from file"""
        if self.registry_file.exists():
            try:
                with open(self.registry_file, 'r') as f:
                    self.models = json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load model registry: {e}")
                self.models = {}
        else:
            self.models = {}
    
    def save_registry(self):
        """Save model registry to file"""
        try:
            with open(self.registry_file, 'w') as f:
                json.dump(self.models, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save model registry: {e}")
    
    def register_model(self, model_info: ModelInfo):
        """Register a model in the registry"""
        self.models[model_info.name] = asdict(model_info)
        self.save_registry()
    
    def update_performance(self, performance: ModelPerformance):
        """Update model performance data"""
        if performance.model_name in self.models:
            self.models[performance.model_name]["performance"] = asdict(performance)
            self.save_registry()
    
    def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get model information"""
        return self.models.get(model_name)
    
    def list_models(self) -> List[str]:
        """List all registered models"""
        return list(self.models.keys())
    
    def get_best_model_for_task(self, task_type: str = "factual") -> Optional[str]:
        """Get the best model for a specific task type"""
        best_model = None
        best_score = 0
        
        for model_name, model_data in self.models.items():
            performance = model_data.get("performance")
            if performance:
                # Simple scoring based on tokens per second and response time
                score = performance["avg_tokens_per_second"] / (performance["avg_response_time"] + 1)
                if score > best_score:
                    best_score = score
                    best_model = model_name
        
        return best_model


class AdvancedModelManager:
    """Advanced model management with optimization and monitoring"""
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.api_url = f"{base_url}/api"
        self.optimizer = ModelOptimizer(base_url)
        self.registry = ModelRegistry()
    
    def discover_and_register_models(self):
        """Discover available models and register them"""
        try:
            response = requests.get(f"{self.api_url}/tags", timeout=10)
            if response.status_code == 200:
                data = response.json()
                models = data.get("models", [])
                
                for model_data in models:
                    model_info = ModelInfo(
                        name=model_data["name"],
                        size=model_data["size"],
                        modified_at=model_data["modified_at"],
                        digest=model_data["digest"],
                        family=model_data.get("details", {}).get("family", ""),
                        parameter_size=model_data.get("details", {}).get("parameter_size", ""),
                        quantization_level=model_data.get("details", {}).get("quantization_level", "")
                    )
                    
                    self.registry.register_model(model_info)
                    console.print(f"[green]✅ Registered model: {model_info.name}[/green]")
        
        except Exception as e:
            logger.error(f"Failed to discover models: {e}")
    
    def benchmark_all_models(self):
        """Benchmark all available models"""
        models = self.registry.list_models()
        
        for model_name in models:
            try:
                performance = self.optimizer.benchmark_model_comprehensive(model_name)
                self.registry.update_performance(performance)
                console.print(f"[green]✅ Benchmarked {model_name}[/green]")
            except Exception as e:
                console.print(f"[red]❌ Failed to benchmark {model_name}: {e}[/red]")
    
    def show_model_comparison(self):
        """Show a comparison table of all models"""
        table = Table(title="Model Performance Comparison")
        
        table.add_column("Model", style="cyan")
        table.add_column("Size", style="magenta")
        table.add_column("Tokens/sec", style="green")
        table.add_column("Avg Response Time", style="yellow")
        table.add_column("Last Tested", style="blue")
        
        for model_name in self.registry.list_models():
            model_data = self.registry.get_model_info(model_name)
            if model_data:
                size_mb = model_data.get("size", 0) / (1024 * 1024)
                performance = model_data.get("performance", {})
                
                table.add_row(
                    model_name,
                    f"{size_mb:.1f} MB",
                    f"{performance.get('avg_tokens_per_second', 0):.1f}",
                    f"{performance.get('avg_response_time', 0):.2f}s",
                    performance.get('last_tested', 'Never')[:10] if performance.get('last_tested') else 'Never'
                )
        
        console.print(table)
    
    def get_recommendation(self, use_case: str = "kcc_assistant") -> Dict[str, Any]:
        """Get model and settings recommendation for specific use case"""
        best_model = self.registry.get_best_model_for_task("factual")
        
        if not best_model:
            # Fallback to first available model
            models = self.registry.list_models()
            best_model = models[0] if models else None
        
        if best_model:
            optimal_settings = self.optimizer.get_optimal_settings(best_model, "factual")
            
            return {
                "recommended_model": best_model,
                "optimal_settings": optimal_settings,
                "model_info": self.registry.get_model_info(best_model)
            }
        
        return {"error": "No models available"}


def main():
    """Main function for model management CLI"""
    console.print(Panel.fit(
        "[bold blue]Advanced Model Manager[/bold blue]\n"
        "Manage, optimize, and benchmark your AI models",
        title="🤖 Model Management"
    ))
    
    manager = AdvancedModelManager()
    
    # Discover and register models
    console.print("[yellow]🔍 Discovering models...[/yellow]")
    manager.discover_and_register_models()
    
    # Show current models
    manager.show_model_comparison()
    
    # Get recommendation for KCC assistant
    recommendation = manager.get_recommendation("kcc_assistant")
    
    if "error" not in recommendation:
        console.print(Panel(
            f"[bold green]Recommended Model:[/bold green] {recommendation['recommended_model']}\n\n"
            f"[bold blue]Optimal Settings:[/bold blue]\n" +
            "\n".join([f"  {k}: {v}" for k, v in recommendation['optimal_settings'].items()]),
            title="💡 Recommendation for KCC Assistant"
        ))
    else:
        console.print(f"[red]❌ {recommendation['error']}[/red]")


if __name__ == "__main__":
    main()
