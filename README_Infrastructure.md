# KCC Query Assistant - Infrastructure Setup

This document covers the offline AI service infrastructure for the KCC Query Assistant project.

## 🏗️ Infrastructure Components

### Core Components Created

1. **`offline_ai_service.py`** - Main AI service wrapper for Ollama
2. **`setup_ollama.py`** - Automated Ollama installation and model setup
3. **`model_manager.py`** - Advanced model management and optimization
4. **`test_infrastructure.py`** - Infrastructure testing and verification
5. **`requirements.txt`** - Python dependencies

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Set Up Ollama and Models

```bash
python setup_ollama.py
```

This script will:
- Install Ollama (if not already installed)
- Start the Ollama service
- Download a recommended AI model (Gemma 2B by default)
- Verify the setup

### 3. Test the Infrastructure

```bash
python test_infrastructure.py
```

This will verify that all components are working correctly.

## 📋 Detailed Setup Instructions

### Manual Ollama Installation

If the automated setup doesn't work, you can install Ollama manually:

**Windows:**
1. Download from https://ollama.com/download/windows
2. Run the installer
3. Open Command Prompt and run: `ollama serve`

**macOS:**
```bash
brew install ollama
ollama serve
```

**Linux:**
```bash
curl -fsSL https://ollama.com/install.sh | sh
ollama serve
```

### Download Models

After Ollama is running, download a model:

```bash
# Recommended for KCC Assistant (fast, good quality)
ollama pull gemma2:2b

# Alternative options
ollama pull gemma2:9b      # Better quality, larger size
ollama pull llama3.2:3b    # Good alternative
ollama pull qwen2.5:3b     # Multilingual support
```

## 🔧 Configuration

### Model Selection

The infrastructure supports multiple models. Choose based on your needs:

- **gemma2:2b** (1.6GB) - Fast, good for basic queries
- **gemma2:9b** (5.4GB) - Better quality responses
- **llama3.2:3b** (2.0GB) - Good balance of speed and quality
- **qwen2.5:3b** (1.9GB) - Excellent for multilingual support

### Performance Optimization

Use the model manager to optimize settings:

```python
from model_manager import AdvancedModelManager

manager = AdvancedModelManager()
recommendation = manager.get_recommendation("kcc_assistant")
print(recommendation)
```

## 🧪 Testing and Verification

### Basic Test

```python
from offline_ai_service import OfflineAIService, GenerationRequest

# Initialize service
ai_service = OfflineAIService()

# Check if ready
if ai_service.is_service_ready():
    # Test generation
    request = GenerationRequest(
        prompt="What are the best practices for wheat cultivation?",
        temperature=0.3,
        max_tokens=200
    )
    
    response = ai_service.generate_text(request)
    print(response.response)
```

### Comprehensive Testing

Run the test suite:

```bash
python test_infrastructure.py
```

Expected output:
```
✅ Python Imports - PASS
✅ Data Files - PASS  
✅ Ollama Connection - PASS
✅ AI Service - PASS
✅ Data Loading - PASS
```

## 🔍 Troubleshooting

### Common Issues

**1. Ollama not running**
```bash
# Start Ollama service
ollama serve
```

**2. No models available**
```bash
# Download a model
ollama pull gemma2:2b
```

**3. Connection refused**
- Check if Ollama is running on port 11434
- Verify firewall settings
- Try restarting Ollama service

**4. Import errors**
```bash
# Install missing dependencies
pip install -r requirements.txt
```

### Verification Commands

```bash
# Check Ollama status
curl http://localhost:11434/

# List available models
ollama list

# Test model
ollama run gemma2:2b "Hello, can you help with agriculture?"
```

## 📊 Performance Monitoring

### Benchmark Models

```python
from model_manager import AdvancedModelManager

manager = AdvancedModelManager()
manager.benchmark_all_models()
manager.show_model_comparison()
```

### Monitor Performance

The infrastructure includes performance tracking:
- Tokens per second
- Response time
- Memory usage
- Quality metrics

## 🔧 Advanced Configuration

### Custom Model Settings

```python
from offline_ai_service import OfflineAIService, GenerationRequest

ai_service = OfflineAIService()

# Custom settings for factual responses
request = GenerationRequest(
    prompt="Your question here",
    system_prompt="You are an agricultural expert. Provide accurate, practical advice.",
    temperature=0.3,  # Lower for more factual responses
    max_tokens=500,
    top_p=0.8,
    top_k=20
)

response = ai_service.generate_text(request)
```

### Model Switching

```python
# Switch to a different model
ai_service.set_model("gemma2:9b")

# Verify current model
status = ai_service.get_service_status()
print(f"Current model: {status['current_model']}")
```

## 📈 Next Steps

After setting up the infrastructure:

1. **Data Processing Pipeline** - Process and chunk KCC data
2. **Embedding Generation** - Create vector embeddings
3. **Vector Store Setup** - Set up FAISS/ChromaDB
4. **RAG Implementation** - Build retrieval-augmented generation
5. **Web Interface** - Create Streamlit application

## 🆘 Support

If you encounter issues:

1. Run `python test_infrastructure.py` to identify problems
2. Check the troubleshooting section above
3. Verify all dependencies are installed
4. Ensure Ollama service is running

## 📝 Files Overview

- `offline_ai_service.py` - Core AI service (300 lines)
- `setup_ollama.py` - Setup automation (300 lines)
- `model_manager.py` - Model management (300 lines)
- `test_infrastructure.py` - Testing utilities (200 lines)
- `requirements.txt` - Dependencies (25 lines)

Total: ~1,125 lines of infrastructure code ready for the KCC Query Assistant!
