#!/usr/bin/env python3
"""
Data Analysis Example using Offline AI Service
Demonstrates AI-powered analysis of CSV data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from offline_ai_service import OfflineAIService, GenerationRequest
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import track

console = Console()

class AIDataAnalyzer:
    def __init__(self):
        self.ai_service = OfflineAIService()
        
        if not self.ai_service.is_service_ready():
            raise RuntimeError("AI service is not ready. Please run setup_ollama.py first.")
    
    def load_data(self, file_path: str) -> pd.DataFrame:
        """Load CSV data"""
        try:
            df = pd.read_csv(file_path)
            console.print(f"[green]✅ Loaded {len(df)} rows from {file_path}[/green]")
            return df
        except Exception as e:
            console.print(f"[red]❌ Failed to load {file_path}: {e}[/red]")
            raise
    
    def get_data_summary(self, df: pd.DataFrame) -> str:
        """Generate a comprehensive data summary"""
        summary_parts = []
        
        # Basic info
        summary_parts.append(f"Dataset shape: {df.shape[0]} rows, {df.shape[1]} columns")
        summary_parts.append(f"Columns: {', '.join(df.columns.tolist())}")
        
        # Data types
        summary_parts.append("\nData types:")
        for col, dtype in df.dtypes.items():
            summary_parts.append(f"  {col}: {dtype}")
        
        # Missing values
        missing = df.isnull().sum()
        if missing.any():
            summary_parts.append("\nMissing values:")
            for col, count in missing[missing > 0].items():
                summary_parts.append(f"  {col}: {count} ({count/len(df)*100:.1f}%)")
        
        # Numeric columns statistics
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            summary_parts.append("\nNumeric columns summary:")
            for col in numeric_cols:
                stats = df[col].describe()
                summary_parts.append(f"  {col}: mean={stats['mean']:.2f}, std={stats['std']:.2f}, min={stats['min']:.2f}, max={stats['max']:.2f}")
        
        # Categorical columns
        categorical_cols = df.select_dtypes(include=['object']).columns
        if len(categorical_cols) > 0:
            summary_parts.append("\nCategorical columns:")
            for col in categorical_cols:
                unique_count = df[col].nunique()
                summary_parts.append(f"  {col}: {unique_count} unique values")
                if unique_count <= 10:
                    top_values = df[col].value_counts().head(5)
                    summary_parts.append(f"    Top values: {dict(top_values)}")
        
        # Sample data
        summary_parts.append(f"\nFirst 3 rows:\n{df.head(3).to_string()}")
        
        return "\n".join(summary_parts)
    
    def analyze_with_ai(self, data_summary: str, analysis_type: str = "summary") -> str:
        """Use AI to analyze the data summary"""
        console.print(f"[yellow]🤔 Running {analysis_type} analysis...[/yellow]")
        
        try:
            response = self.ai_service.analyze_data(data_summary, analysis_type)
            return response
        except Exception as e:
            console.print(f"[red]❌ AI analysis failed: {e}[/red]")
            return f"Analysis failed: {e}"
    
    def generate_insights(self, df: pd.DataFrame) -> Dict[str, str]:
        """Generate multiple types of insights"""
        data_summary = self.get_data_summary(df)
        
        analyses = {}
        analysis_types = ["summary", "insights", "recommendations", "trends"]
        
        for analysis_type in track(analysis_types, description="Generating insights..."):
            analyses[analysis_type] = self.analyze_with_ai(data_summary, analysis_type)
        
        return analyses
    
    def ask_question_about_data(self, df: pd.DataFrame, question: str) -> str:
        """Ask a specific question about the data"""
        data_summary = self.get_data_summary(df)
        
        prompt = f"""
        Based on the following dataset information, please answer this question: {question}
        
        Dataset Information:
        {data_summary}
        
        Please provide a detailed answer based on the data characteristics shown above.
        """
        
        request = GenerationRequest(
            prompt=prompt,
            system_prompt="You are a data analyst AI. Provide accurate, data-driven answers based on the dataset information provided.",
            temperature=0.3,  # Lower temperature for more factual responses
            max_tokens=1000
        )
        
        try:
            response = self.ai_service.generate_text(request)
            return response.response
        except Exception as e:
            return f"Failed to answer question: {e}"
    
    def compare_datasets(self, df1: pd.DataFrame, df2: pd.DataFrame, 
                        name1: str = "Dataset 1", name2: str = "Dataset 2") -> str:
        """Compare two datasets using AI"""
        summary1 = self.get_data_summary(df1)
        summary2 = self.get_data_summary(df2)
        
        prompt = f"""
        Compare these two datasets and provide insights about their differences, similarities, and potential relationships:
        
        {name1}:
        {summary1}
        
        {name2}:
        {summary2}
        
        Please analyze:
        1. Structural differences (columns, data types, size)
        2. Data quality differences
        3. Potential relationships between the datasets
        4. Recommendations for data integration or analysis
        """
        
        request = GenerationRequest(
            prompt=prompt,
            system_prompt="You are a data analyst AI specializing in dataset comparison and integration.",
            temperature=0.4,
            max_tokens=1500
        )
        
        try:
            response = self.ai_service.generate_text(request)
            return response.response
        except Exception as e:
            return f"Comparison failed: {e}"

def main():
    """Main data analysis demo"""
    console.print(Panel.fit(
        "[bold blue]AI-Powered Data Analysis Demo[/bold blue]\n"
        "Analyzing your CSV files with Gemma 3",
        title="📊 Data Analysis"
    ))
    
    try:
        analyzer = AIDataAnalyzer()
        
        # Load the data files
        cleaned_data = analyzer.load_data("Cleaned_Data.csv")
        uncleaned_data = analyzer.load_data("Uncleaned_Data.csv")
        
        # Show basic info
        table = Table(title="Dataset Overview")
        table.add_column("Dataset", style="cyan")
        table.add_column("Rows", style="magenta")
        table.add_column("Columns", style="green")
        
        table.add_row("Cleaned Data", str(len(cleaned_data)), str(len(cleaned_data.columns)))
        table.add_row("Uncleaned Data", str(len(uncleaned_data)), str(len(uncleaned_data.columns)))
        
        console.print(table)
        
        # Generate insights for cleaned data
        console.print("\n[bold blue]Analyzing Cleaned Data...[/bold blue]")
        cleaned_insights = analyzer.generate_insights(cleaned_data)
        
        for analysis_type, insight in cleaned_insights.items():
            console.print(Panel(
                insight,
                title=f"📈 {analysis_type.title()} - Cleaned Data",
                border_style="green"
            ))
        
        # Compare datasets
        console.print("\n[bold blue]Comparing Datasets...[/bold blue]")
        comparison = analyzer.compare_datasets(
            cleaned_data, uncleaned_data,
            "Cleaned Data", "Uncleaned Data"
        )
        
        console.print(Panel(
            comparison,
            title="🔍 Dataset Comparison",
            border_style="yellow"
        ))
        
        # Interactive Q&A
        console.print("\n[bold blue]Interactive Data Q&A[/bold blue]")
        console.print("Ask questions about your data (type 'quit' to exit):")
        
        while True:
            question = console.input("\n[green]Your question: [/green]")
            
            if question.lower() in ['quit', 'exit', 'q']:
                break
            
            answer = analyzer.ask_question_about_data(cleaned_data, question)
            console.print(Panel(
                answer,
                title="🤖 AI Answer",
                border_style="blue"
            ))
    
    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

def quick_analysis_example():
    """Quick analysis example without interaction"""
    console.print(Panel.fit(
        "[bold blue]Quick Data Analysis[/bold blue]",
        title="⚡ Quick Demo"
    ))
    
    try:
        analyzer = AIDataAnalyzer()
        
        # Load and analyze cleaned data
        df = analyzer.load_data("Cleaned_Data.csv")
        
        # Get AI summary
        data_summary = analyzer.get_data_summary(df)
        ai_summary = analyzer.analyze_with_ai(data_summary, "summary")
        
        console.print(Panel(
            ai_summary,
            title="📊 AI Data Summary",
            border_style="green"
        ))
        
        # Get recommendations
        recommendations = analyzer.analyze_with_ai(data_summary, "recommendations")
        
        console.print(Panel(
            recommendations,
            title="💡 AI Recommendations",
            border_style="blue"
        ))
        
    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="AI Data Analysis Examples")
    parser.add_argument("--mode", choices=["full", "quick"], 
                       default="full", help="Analysis mode")
    
    args = parser.parse_args()
    
    if args.mode == "full":
        main()
    elif args.mode == "quick":
        quick_analysis_example()
