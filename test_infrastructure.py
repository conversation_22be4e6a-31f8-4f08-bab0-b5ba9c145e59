#!/usr/bin/env python3
"""
Test script for KCC Query Assistant Infrastructure
Verifies that all components are working correctly
"""

import sys
from pathlib import Path

from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()


def test_imports():
    """Test that all required modules can be imported"""
    console.print("[yellow]🔍 Testing imports...[/yellow]")
    
    try:
        from offline_ai_service import OfflineAIService, GenerationRequest, ModelManager
        console.print("[green]✅ offline_ai_service imported successfully[/green]")
    except ImportError as e:
        console.print(f"[red]❌ Failed to import offline_ai_service: {e}[/red]")
        return False
    
    try:
        from model_manager import AdvancedModelManager, ModelOptimizer, ModelRegistry
        console.print("[green]✅ model_manager imported successfully[/green]")
    except ImportError as e:
        console.print(f"[red]❌ Failed to import model_manager: {e}[/red]")
        return False
    
    return True


def test_data_files():
    """Test that data files are accessible"""
    console.print("[yellow]🔍 Testing data files...[/yellow]")
    
    cleaned_data = Path("Cleaned_Data.csv")
    uncleaned_data = Path("Uncleaned_Data.csv")
    
    if cleaned_data.exists():
        console.print(f"[green]✅ Found Cleaned_Data.csv ({cleaned_data.stat().st_size / 1024 / 1024:.1f} MB)[/green]")
    else:
        console.print("[red]❌ Cleaned_Data.csv not found[/red]")
        return False
    
    if uncleaned_data.exists():
        console.print(f"[green]✅ Found Uncleaned_Data.csv ({uncleaned_data.stat().st_size / 1024 / 1024:.1f} MB)[/green]")
    else:
        console.print("[red]❌ Uncleaned_Data.csv not found[/red]")
        return False
    
    return True


def test_ollama_connection():
    """Test Ollama connection"""
    console.print("[yellow]🔍 Testing Ollama connection...[/yellow]")
    
    try:
        from offline_ai_service import ModelManager
        
        model_manager = ModelManager()
        
        if model_manager.is_ollama_running():
            console.print("[green]✅ Ollama service is running[/green]")
            
            models = model_manager.list_models()
            if models:
                console.print(f"[green]✅ Found {len(models)} model(s)[/green]")
                for model in models:
                    console.print(f"  • {model}")
                return True
            else:
                console.print("[yellow]⚠️  Ollama is running but no models found[/yellow]")
                console.print("[blue]💡 Run 'python setup_ollama.py' to download models[/blue]")
                return False
        else:
            console.print("[red]❌ Ollama service is not running[/red]")
            console.print("[blue]💡 Run 'python setup_ollama.py' to set up Ollama[/blue]")
            return False
    
    except Exception as e:
        console.print(f"[red]❌ Error testing Ollama: {e}[/red]")
        return False


def test_ai_service():
    """Test AI service functionality"""
    console.print("[yellow]🔍 Testing AI service...[/yellow]")
    
    try:
        from offline_ai_service import OfflineAIService, GenerationRequest
        
        ai_service = OfflineAIService()
        
        if not ai_service.is_service_ready():
            console.print("[red]❌ AI service is not ready[/red]")
            return False
        
        console.print("[green]✅ AI service is ready[/green]")
        
        # Test basic generation
        request = GenerationRequest(
            prompt="What is agriculture?",
            max_tokens=50,
            temperature=0.3
        )
        
        response = ai_service.generate_text(request)
        
        if response.response:
            console.print("[green]✅ Text generation test successful[/green]")
            console.print(f"[blue]Sample response: {response.response[:100]}...[/blue]")
            return True
        else:
            console.print("[red]❌ Text generation test failed - empty response[/red]")
            return False
    
    except Exception as e:
        console.print(f"[red]❌ Error testing AI service: {e}[/red]")
        return False


def test_data_loading():
    """Test data loading functionality"""
    console.print("[yellow]🔍 Testing data loading...[/yellow]")
    
    try:
        import pandas as pd
        
        # Test loading cleaned data
        df_cleaned = pd.read_csv("Cleaned_Data.csv")
        console.print(f"[green]✅ Loaded cleaned data: {len(df_cleaned)} rows, {len(df_cleaned.columns)} columns[/green]")
        
        # Test loading uncleaned data
        df_uncleaned = pd.read_csv("Uncleaned_Data.csv")
        console.print(f"[green]✅ Loaded uncleaned data: {len(df_uncleaned)} rows, {len(df_uncleaned.columns)} columns[/green]")
        
        # Show sample data structure
        console.print("[blue]📋 Cleaned data columns:[/blue]")
        for col in df_cleaned.columns:
            console.print(f"  • {col}")
        
        return True
    
    except Exception as e:
        console.print(f"[red]❌ Error loading data: {e}[/red]")
        return False


def show_system_status():
    """Show comprehensive system status"""
    table = Table(title="System Status")
    
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Details", style="blue")
    
    # Test each component
    components = [
        ("Python Imports", test_imports, "Core modules"),
        ("Data Files", test_data_files, "CSV datasets"),
        ("Ollama Connection", test_ollama_connection, "AI service backend"),
        ("AI Service", test_ai_service, "Text generation"),
        ("Data Loading", test_data_loading, "Pandas integration")
    ]
    
    all_passed = True
    
    for name, test_func, description in components:
        try:
            result = test_func()
            status = "✅ PASS" if result else "❌ FAIL"
            if not result:
                all_passed = False
        except Exception as e:
            status = f"❌ ERROR: {str(e)[:50]}"
            all_passed = False
        
        table.add_row(name, status, description)
    
    console.print(table)
    
    return all_passed


def main():
    """Main test function"""
    console.print(Panel.fit(
        "[bold blue]KCC Query Assistant - Infrastructure Test[/bold blue]\n"
        "Verifying that all components are working correctly",
        title="🧪 System Test"
    ))
    
    # Run comprehensive status check
    all_passed = show_system_status()
    
    if all_passed:
        console.print(Panel.fit(
            "[bold green]🎉 All tests passed![/bold green]\n\n"
            "✅ Infrastructure is ready\n"
            "✅ Data files are accessible\n"
            "✅ AI service is functional\n\n"
            "You can now proceed to build the RAG pipeline and web interface!",
            title="✅ Success"
        ))
    else:
        console.print(Panel.fit(
            "[bold red]❌ Some tests failed[/bold red]\n\n"
            "Please check the issues above and:\n"
            "• Run 'python setup_ollama.py' if Ollama needs setup\n"
            "• Install missing dependencies with 'pip install -r requirements.txt'\n"
            "• Ensure data files are in the correct location",
            title="⚠️  Issues Found"
        ))
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
