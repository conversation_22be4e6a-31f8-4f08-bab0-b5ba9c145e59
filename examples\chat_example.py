#!/usr/bin/env python3
"""
Chat Example using Offline AI Service
Demonstrates basic chat functionality with Gemma 3
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from offline_ai_service import OfflineAIService, GenerationRequest
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt

console = Console()

def main():
    """Main chat loop"""
    console.print(Panel.fit(
        "[bold blue]Offline AI Chat with Gemma 3[/bold blue]\n"
        "Type 'quit' to exit, 'clear' to clear history, 'status' for service info",
        title="🤖 AI Chat"
    ))
    
    # Initialize the AI service
    try:
        ai_service = OfflineAIService()
        
        if not ai_service.is_service_ready():
            console.print("[red]❌ AI service is not ready. Please run setup_ollama.py first.[/red]")
            return
        
        console.print("[green]✅ AI service is ready![/green]")
        
        # Show current model
        status = ai_service.get_service_status()
        console.print(f"[blue]Current model: {status['current_model']}[/blue]")
        
    except Exception as e:
        console.print(f"[red]❌ Failed to initialize AI service: {e}[/red]")
        return
    
    # Chat history for context
    chat_history = []
    
    while True:
        try:
            # Get user input
            user_input = Prompt.ask("\n[bold green]You[/bold green]")
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                console.print("[yellow]👋 Goodbye![/yellow]")
                break
            elif user_input.lower() == 'clear':
                chat_history = []
                console.print("[yellow]🧹 Chat history cleared![/yellow]")
                continue
            elif user_input.lower() == 'status':
                status = ai_service.get_service_status()
                console.print(Panel(
                    f"Service Ready: {status['service_ready']}\n"
                    f"Current Model: {status['current_model']}\n"
                    f"Available Models: {', '.join(status['available_models'])}",
                    title="🔍 Service Status"
                ))
                continue
            elif user_input.lower().startswith('model '):
                # Change model
                new_model = user_input[6:].strip()
                if ai_service.set_model(new_model):
                    console.print(f"[green]✅ Switched to model: {new_model}[/green]")
                else:
                    console.print(f"[red]❌ Failed to switch to model: {new_model}[/red]")
                continue
            
            # Add user message to history
            chat_history.append({"role": "user", "content": user_input})
            
            # Generate response
            console.print("[yellow]🤔 Thinking...[/yellow]")
            
            try:
                # Use chat completion for better context handling
                response = ai_service.chat_completion(chat_history)
                
                # Add assistant response to history
                chat_history.append({"role": "assistant", "content": response})
                
                # Display response
                console.print(Panel(
                    response,
                    title="🤖 AI Assistant",
                    border_style="blue"
                ))
                
            except Exception as e:
                console.print(f"[red]❌ Error generating response: {e}[/red]")
                # Remove the user message from history if generation failed
                chat_history.pop()
        
        except KeyboardInterrupt:
            console.print("\n[yellow]👋 Goodbye![/yellow]")
            break
        except Exception as e:
            console.print(f"[red]❌ Unexpected error: {e}[/red]")

def streaming_chat_example():
    """Example of streaming chat responses"""
    console.print(Panel.fit(
        "[bold blue]Streaming Chat Example[/bold blue]",
        title="🚀 Streaming Demo"
    ))
    
    ai_service = OfflineAIService()
    
    if not ai_service.is_service_ready():
        console.print("[red]❌ AI service is not ready.[/red]")
        return
    
    prompt = "Tell me a short story about a robot learning to paint."
    
    request = GenerationRequest(
        prompt=prompt,
        temperature=0.8,
        max_tokens=500
    )
    
    console.print(f"[green]Prompt:[/green] {prompt}")
    console.print("[blue]Streaming response:[/blue]")
    
    try:
        response_text = ""
        for chunk in ai_service.generate_text_stream(request):
            console.print(chunk, end="")
            response_text += chunk
        
        console.print("\n\n[green]✅ Streaming complete![/green]")
        
    except Exception as e:
        console.print(f"\n[red]❌ Streaming error: {e}[/red]")

def benchmark_example():
    """Example of benchmarking the AI model"""
    console.print(Panel.fit(
        "[bold blue]Model Benchmark Example[/bold blue]",
        title="⚡ Performance Test"
    ))
    
    ai_service = OfflineAIService()
    
    if not ai_service.is_service_ready():
        console.print("[red]❌ AI service is not ready.[/red]")
        return
    
    # Run benchmark
    console.print("[yellow]🔄 Running benchmark...[/yellow]")
    
    benchmark = ai_service.model_manager.benchmark_model(
        ai_service.current_model,
        "Write a haiku about artificial intelligence."
    )
    
    if benchmark:
        console.print(Panel(
            f"Model: {benchmark['model']}\n"
            f"Total Time: {benchmark['total_time']:.2f}s\n"
            f"Tokens Generated: {benchmark['eval_count']}\n"
            f"Tokens/Second: {benchmark.get('tokens_per_second', 0):.2f}\n"
            f"Prompt Tokens: {benchmark['prompt_eval_count']}\n"
            f"Response Length: {benchmark['response_length']} chars",
            title="📊 Benchmark Results",
            border_style="green"
        ))
    else:
        console.print("[red]❌ Benchmark failed[/red]")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Offline AI Chat Examples")
    parser.add_argument("--mode", choices=["chat", "stream", "benchmark"], 
                       default="chat", help="Example mode to run")
    
    args = parser.parse_args()
    
    if args.mode == "chat":
        main()
    elif args.mode == "stream":
        streaming_chat_example()
    elif args.mode == "benchmark":
        benchmark_example()
